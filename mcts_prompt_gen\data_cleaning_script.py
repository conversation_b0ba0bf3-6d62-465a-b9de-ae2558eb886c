#!/usr/bin/env python3
"""
Data Cleaning Script for Image Dataset Processing

This script processes image datasets from multiple directories and extracts prompt metadata
with quality labels based on user selection patterns.

Author: AI Assistant
Date: 2025-06-23
"""

import os
import sys
import re
from pathlib import Path
from typing import List, Tuple, Dict, Set
from collections import defaultdict
import dill
from PIL import Image

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
from utils import image_info, is_image_filename, stderr_print


class DataCleaner:
    """Main class for processing image datasets and extracting prompt metadata."""
    
    def __init__(self):
        self.input_directories = [
            r"F:\SD-webui\ComfyUI\output",
            r"F:\SD-webui\ComfyUI2\output", 
            r"F:\SD-webui\ComfyUI_windows_portable_old\ComfyUI\output",
            r"F:\SD-webui\ComfyUI_windows_portable_nvidia_cu121_or_cpu_03_03_2024\ComfyUI_windows_portable\ComfyUI\output",
            r"F:\Code\PlayGround\yeflib\results",
            r"F:\SD-webui\gallery\server"
        ]
        
        # Statistics tracking
        self.stats = {
            'total_images': 0,
            'good_images': 0,
            'normal_images': 0,
            'prompt_extraction_success': 0,
            'prompt_extraction_failed': 0,
            'directories_processed': 0,
            'date_directories_found': 0
        }
        
        self.failed_extractions = []  # List of files where prompt extraction failed
        
    def is_date_directory(self, dirname: str) -> bool:
        """Check if directory name matches date pattern (e.g., '2025-03-22')."""
        date_pattern = r'^\d{4}-\d{2}-\d{2}'
        return bool(re.match(date_pattern, dirname))
    
    def is_sel_directory(self, dirname: str) -> bool:
        """Check if directory name starts with 'sel'."""
        return dirname.lower().startswith('sel')
    
    def find_sel_images_recursive(self, base_path: Path) -> Set[str]:
        """
        Recursively find all image filenames in 'sel*' subdirectories.
        
        Args:
            base_path: Path to search for sel directories
            
        Returns:
            Set of image filenames (just the filename, not full path)
        """
        sel_images = set()
        
        def scan_directory(path: Path):
            if not path.exists() or not path.is_dir():
                return
                
            try:
                for item in path.iterdir():
                    if item.is_dir() and self.is_sel_directory(item.name):
                        # This is a sel directory, scan it recursively
                        scan_sel_directory(item)
                    elif item.is_file() and is_image_filename(item.name):
                        # This is an image file in the base directory
                        pass  # We don't collect these here, only from sel directories
            except (PermissionError, OSError) as e:
                stderr_print(f"Warning: Cannot access directory {path}: {e}")
        
        def scan_sel_directory(sel_path: Path):
            """Recursively scan a sel directory for images."""
            try:
                for item in sel_path.rglob('*'):
                    if item.is_file() and is_image_filename(item.name):
                        sel_images.add(item.name)
            except (PermissionError, OSError) as e:
                stderr_print(f"Warning: Cannot access sel directory {sel_path}: {e}")
        
        scan_directory(base_path)
        return sel_images
    
    def process_date_directory(self, date_dir_path: Path) -> List[Tuple[str, str]]:
        """
        Process a single date directory to determine image quality labels.
        
        Args:
            date_dir_path: Path to the date directory
            
        Returns:
            List of tuples (full_image_path, quality_label)
        """
        results = []
        
        # Find all images in sel* subdirectories
        sel_images = self.find_sel_images_recursive(date_dir_path)
        
        # Process all images in the main date directory
        try:
            for item in date_dir_path.iterdir():
                if item.is_file() and is_image_filename(item.name):
                    full_path = str(item.absolute())
                    
                    # Determine quality based on presence in sel directories
                    if item.name in sel_images:
                        quality = "good"
                        self.stats['good_images'] += 1
                    else:
                        quality = "normal"
                        self.stats['normal_images'] += 1
                    
                    results.append((full_path, quality))
                    self.stats['total_images'] += 1
                    
        except (PermissionError, OSError) as e:
            stderr_print(f"Warning: Cannot access date directory {date_dir_path}: {e}")
        
        return results
    
    def collect_all_images_with_quality(self) -> List[Tuple[str, str]]:
        """
        Phase 1: Collect all image filenames with quality labels.
        
        Returns:
            List of tuples (full_image_path, quality_label)
        """
        all_images = []
        
        for input_dir in self.input_directories:
            input_path = Path(input_dir)
            
            if not input_path.exists():
                stderr_print(f"Warning: Input directory does not exist: {input_dir}")
                continue
                
            stderr_print(f"Processing directory: {input_dir}")
            self.stats['directories_processed'] += 1
            
            try:
                # Look for date-based subdirectories
                for item in input_path.iterdir():
                    if item.is_dir() and self.is_date_directory(item.name):
                        stderr_print(f"  Found date directory: {item.name}")
                        self.stats['date_directories_found'] += 1
                        
                        date_results = self.process_date_directory(item)
                        all_images.extend(date_results)
                        
                        stderr_print(f"    Processed {len(date_results)} images")
                        
            except (PermissionError, OSError) as e:
                stderr_print(f"Warning: Cannot access input directory {input_path}: {e}")
        
        return all_images
    
    def extract_prompt_from_image(self, image_path: str, enhanced_prompts: bool = True) -> str:
        """
        Extract enhanced prompt from a single image using enhanced image_info function.

        Args:
            image_path: Full path to the image file
            enhanced_prompts: Whether to extract enhanced prompts with parameters

        Returns:
            Extracted prompt string (empty if extraction failed)
        """
        try:
            with Image.open(image_path) as img:
                info = image_info(img, image_path, enhanced_prompts=enhanced_prompts)
                prompt = info.get('prompt', '').strip()

                if prompt:
                    self.stats['prompt_extraction_success'] += 1

                    # Log additional parameter extraction statistics
                    if enhanced_prompts and 'workflow_params' in info:
                        if not hasattr(self.stats, 'enhanced_extractions'):
                            self.stats['enhanced_extractions'] = 0
                        self.stats['enhanced_extractions'] += 1

                    return prompt
                else:
                    self.stats['prompt_extraction_failed'] += 1
                    self.failed_extractions.append(image_path)
                    return ""

        except Exception as e:
            stderr_print(f"Error processing image {image_path}: {e}")
            self.stats['prompt_extraction_failed'] += 1
            self.failed_extractions.append(image_path)
            return ""
    
    def process_images_with_prompts(self, images_with_quality: List[Tuple[str, str]],
                                  enhanced_prompts: bool = True) -> List[Tuple[str, str, str]]:
        """
        Phase 2: Extract enhanced prompts from all collected images.

        Args:
            images_with_quality: List of (image_path, quality_label) tuples
            enhanced_prompts: Whether to extract enhanced prompts with parameters

        Returns:
            List of (filename, prompt, goodness) tuples
        """
        results = []
        total_images = len(images_with_quality)

        extraction_type = "enhanced prompts" if enhanced_prompts else "basic prompts"
        stderr_print(f"Phase 2: Extracting {extraction_type} from {total_images} images...")

        for i, (image_path, quality) in enumerate(images_with_quality):
            if i % 100 == 0:  # Progress indicator
                stderr_print(f"  Progress: {i}/{total_images} ({i/total_images*100:.1f}%)")

            prompt = self.extract_prompt_from_image(image_path, enhanced_prompts=enhanced_prompts)
            results.append((image_path, prompt, quality))

        return results
    
    def save_results(self, results: List[Tuple[str, str, str]], output_path: str = "promptlabels.pkl"):
        """
        Save results to pickle file using dill.
        
        Args:
            results: List of (filename, prompt, goodness) tuples
            output_path: Path to save the pickle file
        """
        try:
            with open(output_path, 'wb') as f:
                dill.dump(results, f)
            stderr_print(f"Results saved to: {output_path}")
        except Exception as e:
            stderr_print(f"Error saving results: {e}")
            raise
    
    def print_statistics(self):
        """Print processing statistics."""
        stderr_print("\n" + "="*60)
        stderr_print("PROCESSING STATISTICS")
        stderr_print("="*60)
        stderr_print(f"Directories processed: {self.stats['directories_processed']}")
        stderr_print(f"Date directories found: {self.stats['date_directories_found']}")
        stderr_print(f"Total images processed: {self.stats['total_images']}")
        stderr_print(f"  - Good quality images: {self.stats['good_images']}")
        stderr_print(f"  - Normal quality images: {self.stats['normal_images']}")
        stderr_print(f"Prompt extraction success: {self.stats['prompt_extraction_success']}")
        stderr_print(f"Prompt extraction failed: {self.stats['prompt_extraction_failed']}")

        # Enhanced extraction statistics
        if hasattr(self.stats, 'enhanced_extractions'):
            stderr_print(f"Enhanced parameter extraction: {self.stats['enhanced_extractions']}")
            if self.stats['prompt_extraction_success'] > 0:
                enhanced_rate = self.stats['enhanced_extractions'] / self.stats['prompt_extraction_success'] * 100
                stderr_print(f"Enhanced extraction rate: {enhanced_rate:.1f}%")

        if self.stats['total_images'] > 0:
            success_rate = self.stats['prompt_extraction_success'] / self.stats['total_images'] * 100
            stderr_print(f"Success rate: {success_rate:.1f}%")
        
        stderr_print(f"Failed extractions: {len(self.failed_extractions)}")
        if self.failed_extractions:
            stderr_print("First 10 failed files:")
            for failed_file in self.failed_extractions[:10]:
                stderr_print(f"  - {failed_file}")
        stderr_print("="*60)


def main():
    """Main function to run the data cleaning process."""
    cleaner = DataCleaner()
    
    try:
        # Phase 1: Collect all images with quality labels
        stderr_print("Phase 1: Collecting images and determining quality labels...")
        images_with_quality = cleaner.collect_all_images_with_quality()
        
        if not images_with_quality:
            stderr_print("No images found to process!")
            return
        
        # Phase 2: Extract enhanced prompts from images
        results = cleaner.process_images_with_prompts(images_with_quality, enhanced_prompts=True)
        
        # Save results
        cleaner.save_results(results)
        
        # Print statistics
        cleaner.print_statistics()
        
        stderr_print(f"\nProcessing complete! Results saved to promptlabels.pkl")
        
    except KeyboardInterrupt:
        stderr_print("\nProcessing interrupted by user.")
        sys.exit(1)
    except Exception as e:
        stderr_print(f"Error during processing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
